# 移动端App设计风格指南

## 整体风格特征
- **设计语言**: 现代简约风格，以功能性和易用性为核心
- **视觉风格**: 清新自然，偏向食品电商/生鲜配送应用
- **用户体验**: 注重信息层级和操作便捷性

## 色彩系统
- **主品牌色**: 鲜活绿色 (#7ED321 或类似色值) - 用于CTA按钮、状态指示
- **背景色**: 
  - 主背景：纯白色 (#FFFFFF)
  - 次级背景：极浅灰 (#F8F9FA)
- **文字色彩**:
  - 主标题：深灰黑 (#1A1A1A)
  - 副标题：中灰色 (#666666)
  - 说明文字：浅灰色 (#999999)
- **功能性色彩**:
  - 价格/重要信息：深黑色
  - 折扣标签：橙红色或品牌绿
  - 评分星级：金黄色 (#FFD700)

## 布局系统
- **页面结构**: 顶部导航 + 主内容区 + 底部操作栏
- **边距规范**: 
  - 页面左右边距：16-20px
  - 元素间距：8px、12px、16px、24px的倍数关系
- **内容区域**: 使用卡片式布局，圆角半径4-8px
- **栅格系统**: 响应式网格，注重垂直对齐

## 排版系统
- **字体层级**:
  - 页面标题：18-20px，粗体
  - 卡片标题：16px，中等粗细
  - 正文内容：14px，常规
  - 辅助信息：12px，细体
- **行高**: 1.4-1.6倍字号
- **字重**: 主要使用常规(400)和中等(500)字重

## 组件设计规范

### 按钮设计
- **主要按钮**: 品牌绿色背景，白色文字，圆角6-8px，充满宽度
- **次要按钮**: 白色背景，绿色边框，绿色文字
- **数量调节**: 圆形+/-按钮，灰色边框，居中对齐

### 卡片设计
- **产品卡片**: 白色背景，浅灰色边框/阴影，圆角8px
- **图片比例**: 产品图片采用4:3或16:9比例
- **内容层级**: 图片 > 标题 > 价格 > 辅助信息

### 导航设计
- **顶部导航**: 简洁图标 + 页面标题居中 + 功能图标
- **底部导航**: 图标 + 文字标签，突出当前页面状态
- **面包屑**: 使用返回箭头，简洁明了

### 状态指示
- **配送状态**: 绿色圆点 + "Delivered"文字
- **评分显示**: 星级图标 + 数字评分
- **折扣标签**: 小字号，突出显示

## 交互设计原则
- **反馈机制**: 点击有明确的视觉反馈
- **加载状态**: 使用简洁的加载动画
- **错误处理**: 友好的错误提示信息
- **手势操作**: 支持常见的滑动、点击手势

## 图标系统
- **风格**: 线性图标为主，简洁明了
- **尺寸**: 统一使用24px或倍数尺寸
- **颜色**: 默认深灰色，激活状态使用品牌色

## 图片处理
- **产品图片**: 高质量，统一尺寸比例，白色或透明背景
- **圆角处理**: 产品图片统一圆角4-6px
- **占位符**: 使用灰色占位符，保持布局稳定性

## 动效原则
- **过渡动画**: 使用缓动曲线，时长200-300ms
- **页面切换**: 流畅的滑动切换效果
- **微交互**: 按钮点击、数量调节等有轻微动效反馈

## 响应式适配
- **屏幕尺寸**: 优先适配常见手机屏幕尺寸
- **内容缩放**: 保持关键信息可读性
- **触摸友好**: 按钮最小尺寸44px，足够的点击区域

## 设计关键词总结
**现代简约 | 清新自然 | 功能导向 | 卡片布局 | 绿色主题 | 圆角元素 | 层级清晰 | 触摸友好 | 信息密度适中 | 生鲜电商风格**